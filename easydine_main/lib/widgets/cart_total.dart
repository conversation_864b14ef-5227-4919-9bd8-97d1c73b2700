import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../blocs/pos/pos_bloc.dart';
import '../blocs/pos/pos_event.dart';
import '../utils/currency_formatter.dart';
import '../blocs/pos/pos_state.dart';
import '../blocs/cart/cart_bloc.dart';
import '../blocs/cart/cart_state.dart';
import '../dialogs/order_result_dialog.dart';
import '../services/tax_service.dart';
import '../services/bill_modification_service.dart';

class CartTotal extends StatefulWidget {
  final String orderId;
  final String orderType;
  final String tableNumber;
  final bool addToOrder;

  const CartTotal({
    super.key,
    required this.orderId,
    required this.orderType,
    required this.tableNumber,
    this.addToOrder = false,
  });

  @override
  State<CartTotal> createState() => _CartTotalState();
}

class _CartTotalState extends State<CartTotal> {
  bool _isPlacingOrder = false;
  late TaxService _taxService;
  late BillModificationService _billModService;

  @override
  void initState() {
    super.initState();
    _taxService = TaxService();
    _billModService = BillModificationService();

    // Initialize tax service and fetch taxes
    _taxService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        // Listen to CartBloc for place order results
        BlocListener<CartBloc, CartState>(
          listener: (context, cartState) {
            // Only show dialogs if we're in the process of placing an order and not adding to existing order
            if (_isPlacingOrder && !widget.addToOrder) {
              if (cartState is CartLoaded) {
                // Order placed successfully
                setState(() {
                  _isPlacingOrder = false;
                });
                showOrderSuccessDialog(
                  context,
                  title: 'Order Placed Successfully!',
                  message: 'Your order has been sent to the kitchen and will be prepared shortly.',
                );
              } else if (cartState is CartError) {
                // Order failed
                setState(() {
                  _isPlacingOrder = false;
                });
                showOrderErrorDialog(
                  context,
                  title: 'Order Failed',
                  message: cartState.error,
                );
              }
            }
          },
        ),
        // Listen to POSBloc for add to order results
        BlocListener<POSBloc, POSState>(
          listener: (context, posState) {
            // Only show dialogs if we're in the process of adding to order
            if (_isPlacingOrder && widget.addToOrder) {
              if (!posState.isProcessing && posState.error == null) {
                // Items added successfully
                setState(() {
                  _isPlacingOrder = false;
                });
                showOrderSuccessDialog(
                  context,
                  title: 'Items Added Successfully!',
                  message: 'Items have been added to the existing order.',
                );
              } else if (!posState.isProcessing && posState.error != null) {
                // Adding items failed
                setState(() {
                  _isPlacingOrder = false;
                });
                showOrderErrorDialog(
                  context,
                  title: 'Failed to Add Items',
                  message: posState.error!,
                );
              }
            }
          },
        ),
      ],
      child: _buildCartTotalContent(),
    );
  }

  Widget _buildCartTotalContent() {
    return BlocBuilder<POSBloc, POSState>(
      builder: (context, posState) {
        return BlocBuilder<CartBloc, CartState>(
          builder: (context, cartState) {
            final cartItems = cartState.currentCart?.items ?? [];
            final miscItems = cartState.currentCart?.miscItems ?? [];
            final alertItems = cartState.currentCart?.alerts ?? [];

            // Calculate total
            double total = 0.0;
            for (final item in cartItems) {
              total += item.totalPrice;
            }
            for (final miscItem in miscItems) {
              total += miscItem.price;
            }

            return _buildTotalSection(context, posState, cartItems, total);
          },
        );
      },
    );
  }

  Widget _buildTotalSection(BuildContext context, POSState posState, List cartItems, double total) {
    return AnimatedBuilder(
      animation: Listenable.merge([_taxService, _billModService]),
      builder: (context, child) {
        // Calculate bill modifications
        final tipAmount = _billModService.tipAmount;
        final discountAmount = _billModService.discountAmount;

        // Apply discount to subtotal
        final discountedSubtotal = total - discountAmount;
        final finalSubtotal = discountedSubtotal > 0 ? discountedSubtotal : 0;

        // Calculate taxes on discounted subtotal
        final taxBreakdown = _taxService.getTaxBreakdown(finalSubtotal);
        final totalTaxAmount = _taxService.calculateTotalTaxAmount(finalSubtotal);

        // Calculate grand total
        final grandTotal = finalSubtotal + totalTaxAmount + tipAmount;

        return Container(
          padding: EdgeInsets.all(1.h),
          child: Column(
            children: [
              // Priority Dropdown
              _buildPriorityDropdown(context, posState),
              SizedBox(height: 1.h),

              // Subtotal
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Subtotal',
                    style: GoogleFonts.dmSans(color: Colors.white54),
                  ),
                  Text(
                    CurrencyFormatter.format(total),
                    style: GoogleFonts.dmSans(
                        fontWeight: FontWeight.bold, color: Colors.orange),
                  ),
                ],
              ),

              // Discount (if any)
              if (discountAmount > 0) ...[
                SizedBox(height: 0.5.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Discount',
                      style: GoogleFonts.dmSans(color: Colors.red[300]),
                    ),
                    Text(
                      '-${CurrencyFormatter.format(discountAmount)}',
                      style: GoogleFonts.dmSans(
                          fontWeight: FontWeight.bold, color: Colors.red[300]),
                    ),
                  ],
                ),
              ],

              // Tax breakdown
              ...taxBreakdown.map((breakdown) => [
                SizedBox(height: 0.5.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      breakdown.displayText,
                      style: GoogleFonts.dmSans(color: Colors.white54),
                    ),
                    Text(
                      CurrencyFormatter.format(breakdown.amount),
                      style: GoogleFonts.dmSans(
                          fontWeight: FontWeight.bold, color: Colors.orange),
                    ),
                  ],
                ),
              ]).expand((widget) => widget),

              // Tip (if any)
              if (tipAmount > 0) ...[
                SizedBox(height: 0.5.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Tip',
                      style: GoogleFonts.dmSans(color: Colors.green[300]),
                    ),
                    Text(
                      CurrencyFormatter.format(tipAmount),
                      style: GoogleFonts.dmSans(
                          fontWeight: FontWeight.bold, color: Colors.green[300]),
                    ),
                  ],
                ),
              ],

              Divider(height: 1.h),
              // Grand Total
              Row(
                children: [
                  Text(
                    'Total',
                    style: GoogleFonts.dmSans(
                      fontSize: 18,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Spacer(),
                  Text(
                    CurrencyFormatter.format(grandTotal),
                    style: GoogleFonts.dmSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
              Spacer(),
              SizedBox(
                height: 4.h,
                child: ElevatedButton(
                  onPressed: cartItems.isEmpty || posState.isProcessing || _isPlacingOrder
                      ? null
                      : () {
                          setState(() {
                            _isPlacingOrder = true;
                          });

                          if (widget.addToOrder) {
                            // Add items to existing order
                            context.read<POSBloc>().add(AddItemsToOrder(
                                  orderDetailId: widget.orderId,
                                ));
                          } else {
                            // Place new order
                            context.read<POSBloc>().add(PlaceOrder(
                                  orderId: widget.orderId,
                                  priority: posState.currentPriority ?? 1,
                                  orderType: widget.orderType,
                                  tableNumber: widget.tableNumber,
                                ));
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    backgroundColor: const Color.fromRGBO(44, 191, 90, 1),
                    foregroundColor: const Color.fromRGBO(255, 255, 255, 1),
                  ),
                  child: (posState.isProcessing || _isPlacingOrder)
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : Text(widget.addToOrder ? 'Add to Order' : 'Place Order'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
      },
    );
  }

  Widget _buildPriorityDropdown(BuildContext context, POSState state) {
    return DropdownButtonFormField<int>(
      value: state.currentPriority ?? 1,
      decoration: InputDecoration(
        labelText: 'Order Priority',
        labelStyle: GoogleFonts.dmSans(
          color: Colors.white70,
        ),
        enabledBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.white30),
        ),
        focusedBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.white),
        ),
      ),
      dropdownColor: Colors.grey[850],
      style: GoogleFonts.dmSans(color: Colors.white),
      items: [
        DropdownMenuItem(
          value: 1,
          child: Row(
            children: [
              Icon(Icons.flag_outlined, color: Colors.green, size: 8.sp),
              const SizedBox(width: 8),
              Text('Normal Priority',
                  style: GoogleFonts.dmSans(color: Colors.green)),
            ],
          ),
        ),
        DropdownMenuItem(
          value: 2,
          child: Row(
            children: [
              Icon(Icons.flag_outlined, color: Colors.orange, size: 8.sp),
              const SizedBox(width: 8),
              Text('High Priority',
                  style: GoogleFonts.dmSans(color: Colors.orange)),
            ],
          ),
        ),
        DropdownMenuItem(
          value: 3,
          child: Row(
            children: [
              Icon(Icons.flag_outlined, color: Colors.red, size: 8.sp),
              const SizedBox(width: 8),
              Text('Urgent Priority',
                  style: GoogleFonts.dmSans(color: Colors.red)),
            ],
          ),
        ),
      ],
      onChanged: (value) {
        if (value != null) {
          context.read<POSBloc>().add(UpdateOrderPriority(priority: value));
        }
      },
    );
  }


}
