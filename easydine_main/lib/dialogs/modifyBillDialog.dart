
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';

void modifyBillDialog(BuildContext context) {
  final tipController = TextEditingController();
  final discountController = TextEditingController();
  final redeemCodeController = TextEditingController();
  bool hasDiscount = false;

  showDialog(
    context: context,
    builder: (BuildContext context) {
      return StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
            backgroundColor: Color.fromRGBO(34, 34, 34, 1.0),
            elevation: 10,
            title: Row(
              children: [
                Icon(Icons.edit, color: Colors.white, size: 24),
                SizedBox(width: 8),
                Text(
                  'Modify Bill',
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 16),
                  _buildTextField(
                    controller: tipController,
                    labelText: 'Tip',
                    hintText: 'Enter the tip amount',
                    icon: Icons.money,
                    onChanged: (value) {},
                  ),
                  SizedBox(height: 16),
                  _buildTextField(
                    controller: discountController,
                    labelText: 'Discount',
                    hintText: 'Enter the discount amount',
                    icon: Icons.percent,
                    onChanged: (value) {
                      setState(() {
                        hasDiscount = value.isNotEmpty && double.tryParse(value) != null && double.parse(value) > 0;
                      });
                    },
                  ),
                  if (hasDiscount)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0, left: 8.0),
                      child: Text(
                        'Manager approval required for discounts',
                        style: GoogleFonts.poppins(
                          color: Colors.amber,
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            actionsPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            actions: [
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.redAccent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onPressed: () {
                  Navigator.pop(context); // Close the dialog without saving
                },
                child: Text('Cancel',
                    style: GoogleFonts.poppins(color: Colors.white)),
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blueAccent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onPressed: () {
                  // Check if discount requires manager approval
                  if (hasDiscount) {
                    _verifyManagerPin(context, () {
                      _saveBillChanges(
                        context,
                        tipController.text,
                        discountController.text,
                        redeemCodeController.text,
                      );
                    });
                  } else {
                    _saveBillChanges(
                      context,
                      tipController.text,
                      discountController.text,
                      redeemCodeController.text,
                    );
                  }
                },
                child:
                Text('Save', style: GoogleFonts.poppins(color: Colors.white)),
              ),
            ],
          );
        }
      );
    },
  );
}

void _verifyManagerPin(BuildContext context, VoidCallback onSuccess) {
  final pinController = TextEditingController();
  bool isVerifying = false;
  String errorMessage = '';

  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            backgroundColor: Color.fromRGBO(34, 34, 34, 1.0),
            title: Row(
              children: [
                Icon(Icons.security, color: Colors.amber, size: 24),
                SizedBox(width: 8),
                Text(
                  'Manager Approval',
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Please enter manager PIN to approve discount',
                  style: GoogleFonts.poppins(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
                SizedBox(height: 16),
                TextField(
                  controller: pinController,
                  obscureText: true,
                  keyboardType: TextInputType.number,
                  maxLength: 4,
                  style: GoogleFonts.poppins(color: Colors.white),
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: Color.fromRGBO(44, 44, 44, 1.0),
                    prefixIcon: Icon(Icons.pin, color: Colors.white),
                    labelText: 'Manager PIN',
                    labelStyle: GoogleFonts.poppins(color: Colors.white70),
                    hintText: 'Enter 4-digit PIN',
                    hintStyle: GoogleFonts.poppins(color: Colors.white38),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.white38),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.amber),
                    ),
                    errorText: errorMessage.isNotEmpty ? errorMessage : null,
                    errorStyle: GoogleFonts.poppins(color: Colors.redAccent),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context); // Close the PIN dialog
                },
                child: Text(
                  'Cancel',
                  style: GoogleFonts.poppins(color: Colors.white70),
                ),
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onPressed: isVerifying
                    ? null
                    : () async {
                        setState(() {
                          isVerifying = true;
                          errorMessage = '';
                        });
                        
                        // Verify PIN
                        bool isValid = await _checkManagerPin(pinController.text);
                        
                        if (isValid) {
                          Navigator.pop(context); // Close the PIN dialog
                          onSuccess(); // Execute the success callback
                        } else {
                          setState(() {
                            isVerifying = false;
                            errorMessage = 'Invalid PIN. Please try again.';
                          });
                        }
                      },
                child: isVerifying
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.black,
                          strokeWidth: 2,
                        ),
                      )
                    : Text(
                        'Verify',
                        style: GoogleFonts.poppins(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ],
          );
        }
      );
    },
  );
}

Future<bool> _checkManagerPin(String pin) async {
  // In a real app, you would check against stored manager PINs
  // For this example, we'll use a hardcoded PIN or check SharedPreferences
  
  // Simulate network delay
  await Future.delayed(Duration(seconds: 1));
  
  try {
    final prefs = await SharedPreferences.getInstance();
    final managerPin = prefs.getString('manager_pin') ?? '1234'; // Default PIN if not set
    
    return pin == managerPin;
  } catch (e) {
    // Fallback to default PIN if SharedPreferences fails
    return pin == '1234';
  }
}

void _saveBillChanges(
  BuildContext context,
  String tipText,
  String discountText,
  String redeemCode,
) {
  // Parse values
  double? tip = double.tryParse(tipText);
  double? discount = double.tryParse(discountText);
  
  // Create bill modifications object
  final billModifications = {
    'tip': tip ?? 0.0,
    'discount': discount ?? 0.0,
    'redeemCode': redeemCode,
    'timestamp': DateTime.now().toIso8601String(),
  };
  
  // Here you would typically save these changes to your state management system
  // For example, using a BLoC or Provider
  
  // Show success message
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text('Bill modifications saved successfully'),
      backgroundColor: Colors.green,
    ),
  );
  
  // Close the dialog
  Navigator.pop(context);
}

Widget _buildTextField({
  required TextEditingController controller,
  required String labelText,
  required String hintText,
  required IconData icon,
  required Function(String) onChanged,
}) {
  return TextField(
    controller: controller,
    style: GoogleFonts.poppins(color: Colors.white),
    keyboardType: TextInputType.numberWithOptions(decimal: true),
    onChanged: onChanged,
    decoration: InputDecoration(
      filled: true,
      fillColor: Color.fromRGBO(44, 44, 44, 1.0),
      prefixIcon: Icon(icon, color: Colors.white),
      labelText: labelText,
      labelStyle: GoogleFonts.poppins(color: Colors.white70),
      hintText: hintText,
      hintStyle: GoogleFonts.poppins(color: Colors.white38),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Colors.white38),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Colors.blueAccent),
      ),
    ),
  );
}

class CircleClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.addOval(Rect.fromCircle(
      center: Offset(size.width / 2, size.height / 2),
      radius: 100,
    ));
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return false;
  }
}
